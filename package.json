{"name": "elpis-demo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "_ENV='local' nodemon ./service.js", "beta": "_ENV='beta' node ./service.js", "prod": "_ENV='production' node ./service.js", "build:dev": "_ENV='local' node --max_old_space_size=4096 ./build.js", "build:prod": "_ENV='production' node ./build.js"}, "author": "1234wu", "license": "ISC", "devDependencies": {"nodemon": "^3.1.10"}, "dependencies": {"@1234wu/elpis": "^1.0.1"}}