<template>
  <el-row type="flex" align="middle" class="form-item">
    <el-row class="item-label" justify="end">
      <el-row v-if="schema.option?.required" type="flex" class="required"> * </el-row>
      {{ schema.label }}
    </el-row>
    <el-row class="item-value">
      <el-input-number
        v-model="dtoValue"
        v-bind="schema.option"
        :controls="false"
        :placeholder="placeholder"
        class="component"
        :class="validTips ? 'valid-tips' : ''"
        @focus="onFocus"
        @blur="onBlur"
      />
    </el-row>
    <el-row v-if="validTips" class="valid-tips">{{ validTips }}</el-row>
  </el-row>
</template>

<script setup>
import { ref, toRefs, watch, inject, onMounted } from "vue";
const ajv = inject("ajv");
const name = "inputNumber";
const props = defineProps({
  schemaKey: String,
  schema: Object,
  model: Object,
});
const { schemaKey, schema } = props;
const { model } = toRefs(props);

const validTips = ref("");
const dtoValue = ref();
const placeholder = ref("");

const initData = () => {
  dtoValue.value = model.value ?? schema.option?.default;
  validTips.value = null;

  const { minium, maximum } = schema;

  const ruleList = [];
  if (schema.option?.placeholder) {
    ruleList.push(schema.option.placeholder);
  }
  if (minium) {
    ruleList.push(`最小值:${minium}`);
  }
  if (maximum) {
    ruleList.push(`最大值:${maximum}`);
  }

  placeholder.value = ruleList.join("|");
};

onMounted(() => {
  initData();
});

watch(
  [model, schema],
  () => {
    initData();
  },
  { deep: true }
);

const validate = () => {
  const { type } = schema;
  // 校验是否必填
  if (schema.option?.required && !dtoValue.value) {
    validTips.value = "不能为空";
    return false;
  }
  // 校验 schema
  if (dtoValue.value) {
    const validate = ajv.compile(schema);
    const valid = validate(dtoValue.value);
    if (!valid && validate.errors && validate.errors[0]) {
      const { keyword, params } = validate.errors[0];
      if (keyword === "type") {
        validTips.value = `类型错误, 请输入${type}`;
      } else if (keyword === "minimum") {
        validTips.value = `最小值:${params.limit}`;
      } else if (keyword === "maximum") {
        validTips.value = `最大值:${params.limit}`;
      } else {
        console.log(validate.errors[0]);
        validTips.value = "不符合要求";
      }
      return false;
    }
  }
  return true;
};

const getValue = () => {
  return dtoValue.value !== undefined
    ? {
        [schemaKey]: dtoValue.value,
      }
    : {};
};

const onFocus = () => {
  validTips.value = null;
};

const onBlur = () => {
  validate();
};

defineExpose({
  name,
  validate,
  getValue,
});
</script>

<style lang="less" scoped>
:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
