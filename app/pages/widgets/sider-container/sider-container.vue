<template>
  <el-container class="sider-container">
    <el-aside width="200px" class="asider">
      <slot name="menu-content" />
    </el-aside>
    <el-main class="main">
      <slot name="main-content" />
    </el-main>
  </el-container>
</template>

<script setup></script>

<style lang="less" scoped>
.sider-container {
  width: 100%;
  height: 100%;

  .asider {
    border-right: 1px solid #e8e8e8;
    // height: 100%;
  }

  .main {
    overflow: auto;
  }
}

:deep(.el-menu) {
  border-right: 0;
}
</style>
