import { createApp } from 'vue';

// 引入elementui
import ElementUI from 'element-plus';
import 'element-plus/theme-chalk/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css'
import './asserts/custom.css'
import pinia from '$elpisStore'
import { createRouter, createWebHistory } from 'vue-router';


/**
 * vue 页面主入口,用于启动vue
 * @params pageComponent vue 入口组件
 * @params routes 路由别表
 * @params libs 页面所需的外部库
 */
export default (pageComponent, { routes, libs } = {}) => {
  const app = createApp(pageComponent);

  app.use(ElementUI)

  app.use(pinia)

  // 引入第三方包
  if (libs && libs.length) {
    for (let i = 0; i < libs.length; i++) {
      app.use(libs[i]);
    }
  }

  // 页面路由
  if (routes && routes.length) {
    const router = createRouter({
      history: createWebHistory(), // 使用history模式
      routes,
    })
    app.use(router);
    router.isReady().then(() => {
      app.mount('#root')
    })
  } else {
    app.mount('#root')
  }
}