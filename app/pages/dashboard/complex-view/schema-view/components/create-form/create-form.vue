<template>
  <el-drawer v-model="isShow" :destroy-on-close="true" direction="rtl" :size="550">
    <template #header>
      <h4>{{ title }}</h4>
    </template>
    <template #default>
      <SchemaForm ref="schemaFormRef" :schema="components[name]?.schema" />
    </template>
    <template #footer>
      <el-button @click="isShow = false"> 取消 </el-button>
      <el-button type="primary" @click="onSave">{{ saveBtnText }}</el-button>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, inject } from "vue";
import SchemaForm from "$elpisWidgets/schema-form/schema-form.vue";
import { ElNotification } from "element-plus";
const { api, components } = inject("schemaViewData");
const emit = defineEmits(["command"]);
import $curl from "$elpisCommon/curl.js";

// 接收的参数
const name = ref("createForm");

const schemaFormRef = ref(null);
const isShow = ref(false);
const loading = ref(false);
const title = ref("");
const saveBtnText = ref("保存");

const show = () => {
  const { config } = components.value[name.value];
  title.value = config.title;
  saveBtnText.value = config.saveBtnText;
  isShow.value = true;
};
const close = () => {};
const onSave = async () => {
  if (loading.value) return;
  // 校验表单
  if (!schemaFormRef.value.validate()) return false;
  loading.value = true;

  const res = await $curl({
    method: "post",
    url: api.value,
    data: {
      ...schemaFormRef.value.getValue(),
    },
  });
  loading.value = false;

  if (!res || !res.success) {
    return;
  }

  ElNotification({
    title: "创建成功",
    message: "创建成功",
    type: "success",
  });

  close();
  emit("command", {
    event: "loadTableData",
  });
};
defineExpose({ name, show });
</script>

<style lang="less" scoped></style>
