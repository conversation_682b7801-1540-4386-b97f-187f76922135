<template>
  <el-drawer v-model="isShow" direction="rtl" :destroy-on-close="true" :size="550">
    <template #header>
      <h4>{{ title }}</h4>
    </template>
    <template #default>
      <schema-form
        ref="schemaFormRef"
        v-loading="loading"
        :schema="components[name]?.schema"
        :model="dtoModel"
      />
    </template>
    <template #footer>
      <el-button type="primary" @click="save">
        {{ saveBtnText }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, inject } from "vue";
import { ElNotification } from "element-plus";
import SchemaForm from "$elpisWidgets/schema-form/schema-form.vue";
import $curl from "$elpisCommon/curl.js";

const { api, components } = inject("schemaViewData");
const emit = defineEmits(["command"]);

const loading = ref(false);
const schemaFormRef = ref(null);
const name = ref("editForm");
const isShow = ref(false);
const title = ref("");
const saveBtnText = ref("保存");
const mainKey = ref("");
const mainValue = ref();
const dtoModel = ref({});

const show = (rowData) => {
  const { config } = components.value[name.value];
  title.value = config.title;
  saveBtnText.value = config.saveBtnText;
  mainKey.value = config.mainKey; // 表单主键
  mainValue.value = rowData[mainKey.value]; // 表单主键值
  dtoModel.value = {};
  isShow.value = true;
  fetchFormData();
};

const fetchFormData = async () => {
  if (loading.value) return;
  loading.value = true;
  const res = await $curl({
    method: "get",
    url: api.value,
    query: {
      [mainKey.value]: mainValue.value,
    },
  });
  loading.value = false;
  if (!res || !res.success || !res.data) {
    return;
  }
  dtoModel.value = res.data;
};

const close = () => {
  isShow.value = false;
};

const save = async () => {
  if (loading.value) return;
  if (!schemaFormRef.value.validate()) return;
  loading.value = true;
  const res = await $curl({
    method: "put",
    url: api.value,
    data: {
      [mainKey.value]: mainValue.value,
      ...schemaFormRef.value.getValue(),
    },
  });
  console.log("🚀 ~ save ~ res:", res);

  loading.value = false;
  if (!res || !res.success) {
    return;
  }

  ElNotification({
    title: "修改成功",
    message: "修改成功",
    type: "success",
  });

  close();
  emit("command", {
    event: "loadTableData",
  });
};

defineExpose({
  name,
  show,
});
</script>

<style lang="less" scoped></style>
