<template>
  <el-card class="search-panel">
    <SchemaSearchBar :schema="searchSchema" @load="onLoad" @search="onSearch" @reset="onReset" />
  </el-card>
</template>

<script setup>
import { inject } from "vue";
import SchemaSearchBar from "$elpisWidgets/schema-search-bar/schema-search-bar.vue";
const { searchSchema } = inject("schemaViewData");

const emit = defineEmits(["search"]);

const onLoad = (searchObj) => {
  emit("search", searchObj);
};

const onSearch = (searchObj) => {
  emit("search", searchObj);
};

// 重置按钮
const onReset = (searchObj) => {
  emit("search", searchObj);
};
</script>

<style lang="less" scoped>
.search-panel {
  margin: 10px 10px 0 10px;
}
:deep(.el-card__body) {
  padding-bottom: 2px;
}
</style>
