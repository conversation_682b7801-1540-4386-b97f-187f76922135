import md5 from "md5";
import { ElMessage } from "element-plus";
/**
 * 前端封装的 curl 方法
 * @params {Object} options 请求参数
 */


const curl = ({
  url,
  method = "post",
  headers = {},
  query = {},
  data = {},
  responseType = 'json',
  timeout = 60000,
  errorMessage = '网络请求失败，请稍后再试'
}) => {
  // 接口签名处理(让接口变动态)
  const signKey = '620b048b-8ac3-431b-845d-bcaf63ecc738';
  const st = Date.now();

  const dtoHeaders = {
    ...headers,
    s_t: st,
    s_sign: md5(`${signKey}_${st}`)
  }

  if (url.indexOf('/api/proj/') > -1 && window.projKey) {
    dtoHeaders.proj_key = window.projKey

  }


  // 构造请求参数
  const ajaxString = {
    url,
    method,
    headers,
    params: query,
    data,
    responseType,
    timeout,
    headers: dtoHeaders
  }

  return axios.request(ajaxString).then((respense) => {
    const resData = respense.data || {};

    const { success } = resData;

    if (!success) {
      const { message, code } = resData
      if (code === 442) {
        ElMessage.error('请求参数异常');
      } else if (code === 445) {
        ElMessage.error('请求不合法');
      } else if (code === 446) {
        ElMessage.error('缺少项目类型参数');
      } else if (code === 5000) {
        ElMessage.error(message);
      } else {
        ElMessage.error(errorMessage);
      }

      console.error(message)

      return Promise.resolve({ success, code, message })

    }

    // 成功 
    const { data, metadata } = resData;
    return Promise.resolve({ success, data, metadata })

  }).catch((error) => {
    console.log("🚀 ~ returnaxios.request ~ error:", error.response)
    const { message } = error;
    console.log("🚀 ~ returnaxios.request ~ message:", message)

    if (message.match(/timeout/) || error.code === 'ECONNABORTED') {
      return Promise.resolve({
        message: 'request timeout',
        code: 504
      })
    }

    return Promise.resolve(error)
  })
}

export default curl;