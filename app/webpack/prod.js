const webpack = require('webpack');
const webpackProdConfig = require('./config/webpack.prod.js');

module.exports = () => {
  console.log('\nbuilding... \n');
  // 如果你不向 webpack 传入可执行的回调函数， 它会返回一个 webpack Compiler 实例并在其中进行操作
  // const compiler = webpack(webpackProdConfig);
  // 区别在于compiler.run()更具灵活性、控制粒度等适合多次打包, 直接传入一个回调函数 (err, stats)=>{} 则只会执行一次打包, 更适用于生存环境的场景
  webpack(webpackProdConfig, (err, stats) => {
    // 配置文件错误
    if (err) {
      console.log('❗err: \n', err)
      return
    }

    // stats.hasErrors()判断缺失的 module，语法错误等, 还有个 stats.hasWarnings() 方法，可以用来判断是否有警告信息
    if (stats.hasErrors()) {
      const info = stats.toJson()
      console.error(info.errors);
    }

    // process.stdout.write 更高效，适合大量数据输出 (console.log 是基于它实现的)
    process.stdout.write(`✅ ${stats.toString({
      colors: true,// 在控制台输出色彩信息
      modules: false, // 不显示每个模块的打包信息
      children: false, // 不显示子编译任务的信息
      chunks: false, // 不显示每个代码块的信息
      chunkModules: false // 显示代码块中模块的信息
    })}\n`)
  });
}

