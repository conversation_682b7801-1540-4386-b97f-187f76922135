const path = require('path');
const merge = require('webpack-merge');
const webpack = require('webpack')

// 基类配置
const baseConfig = require('./webpack.base.js');

// dev-server 配置
const DEV_SERVER_CONFIG = {
  HOST: '127.0.0.1',
  PORT: 9200,
  HMR_PATH: '__webpack_hmr',
  TIMEOUT: 20000,
}
const { HOST: host, PORT: port, HMR_PATH: hmrPath, TIMEOUT: timeout } = DEV_SERVER_CONFIG;

// 开发阶段的 entry 配置需要加入 hmr 
Object.keys(baseConfig.entry).forEach(v => {
  // 第三方包不作为 hmr 的入口  有时候可能会手动配置第三方库 entry: { vendor: ['vue', 'lodash']} 将其打包到一个单独的文件中
  // 与 splitChunks 的区别: 自动从 node_modules 中提取第三方库。更灵活，适用于复杂的项目。
  if (v !== 'vendor') {
    baseConfig.entry[v] = [
      baseConfig.entry[v], // 主入口文件
      // hmr 更新入口,官方指定的 hmr 路径
      `${require.resolve('webpack-hot-middleware/client')}?path=http://${host}:${port}/${hmrPath}&timeout=${timeout}`,
    ]
  }
})

const webpackConfig = merge.smart(baseConfig, {
  // 指定开发环境
  mode: 'development',
  // soure-map 配置 便于开发时调试
  devtool: 'eval-cheap-module-source-map',
  // 开发环境的 out put 配置
  output: {
    filename: 'js/[name]_[chunkhash:8].bundle.js',
    path: path.resolve(process.cwd(), './app/public/dist/dev/'),
    publicPath: `http://${host}:${port}/public/dist/dev/`, // 输出目录的公共 URL
    globalObject: 'this' // 用于指定 Webpack 打包代码时引用的全局对象。配置成 'this' Webpack 会根据运行环境自动选择正确的全局对象。
  },
  // 开发阶段插件
  plugins: [
    // HotModuleReplacementPlugin 用于实现热模块替换 (Hot Module Replacement - HMR)
    // 模块热替换允许在应用程序运行时替换模块
    // 极大的提升开发效率, 因为能让应用程序一直保持运行状态
    new webpack.HotModuleReplacementPlugin({ multiStep: false }),
  ]
});

module.exports = {
  webpackConfig,  // webpack 配置
  DEV_SERVER_CONFIG // devServer 配置, 暴露给dev.js使用
};