const md5 = require("md5");

/**
 * API 合法性校验
 */
module.exports = (app) => {
  return async (ctx, next) => {
    // 只针对 api 请求做签名校验
    if (ctx.path.indexOf('/api') < 0) {
      return await next()
    }

    const { path, method } = ctx;
    const { headers } = ctx.request;
    const { s_sign: sSgin, s_t: st } = headers;

    const signKey = '620b048b-8ac3-431b-845d-bcaf63ecc738'
    const signature = md5(`${signKey}_${st}`);
    app.logger.info(`[${method} ${path}] signature:${signature}`)

    if (!sSgin || !st || signature !== sSgin.toLowerCase() || Date.now() - st > 10000 * 60) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: 'signature not correct or timeout!!!',
        code: 445
      }
      return
    }
    await next()
  }

}

