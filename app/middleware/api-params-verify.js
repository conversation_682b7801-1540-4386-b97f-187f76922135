const Ajv = require('ajv');
const ajv = new Ajv();


/**
 * API 参数校验
 * 
 */

module.exports = (app) => {

  const $schema = "http://json-schema.org/draft-07/schema#"

  return async (ctx, next) => {
    // 只对 API 做参数校验
    if (ctx.path.indexOf('/api/') < 0) {
      return await next()
    }

    // 获取请求参数
    const { body, query, headers } = ctx.request;
    const { path, method } = ctx;
    app.logger.info(`[${method} ${path}] body:${JSON.stringify(body)}`);
    app.logger.info(`[${method} ${path}] query:${JSON.stringify(query)}`);
    app.logger.info(`[${method} ${path}] headers:${JSON.stringify(headers)}`);

    const schema = app.routerSchema[path]?.[method.toLowerCase()];
    if (!schema) {
      return await next()
    }

    let valid = true;

    //ajv 校验器
    let validate;

    // 校验 headers
    if (valid && headers && schema.headers) {
      schema.headers.$schema = $schema;
      validate = ajv.compile(schema.headers) // 编译函数
      valid = validate(headers) // 校验数据
    }

    // 校验 body
    if (valid && body && schema.body) {
      schema.body.$schema = $schema;
      validate = ajv.compile(schema.body) // 编译函数
      valid = validate(body) // 校验数据
    }

    // 校验 query
    if (valid && query && schema.query) {
      schema.query.$schema = $schema;
      validate = ajv.compile(schema.query) // 编译函数
      valid = validate(query) // 校验数据
    }

    if (!valid) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: `request valid fail: ${ajv.errorsText(validate.errors)} `,
        code: 442
      }
      return
    }

    await next()
  }
}