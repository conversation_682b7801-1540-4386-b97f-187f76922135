/**
 * 运行时异常错误,兜底所有异常
 * @params {object} app  koa实例
 * 
 * Koa 是一个基于 洋葱模型（Onion Model） 的中间件框架,如果下游中间件发生错误并抛出异常，该异常会沿着中间件堆栈向上传递,因此，最外层的中间件（比如你定义的这个中间件）可以捕获整个应用中抛出的错误。
 */

module.exports = (app) => {
  return async (ctx, next) => {
    try {
      await next()
    } catch (err) {
      // 异常处理
      const { status, message, detail } = err;
      app.logger.info(JSON.stringify(err));
      app.logger.error('[ -- ❗exception❗ -- ];', err);
      app.logger.error('[-- ❗exception❗ -- ];', status, message, detail)

      if (message && message.indexOf('template not found') > -1) {
        ctx.status = 302;// 临时重定向
        ctx.redirect(`${app.options?.homePage}`)
        return;
      }

      const resBody = {
        success: false,
        code: 5000,
        message: '网络异常，请稍后再试'
      }

      ctx.status = 200;
      ctx.body = resBody;
    }
  }

}