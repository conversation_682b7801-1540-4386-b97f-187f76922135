// 1. 配置 pages 文件夹下的 enrty.xx.js 文件, 作为 webpack 的入口(entry)文件,
// 2. view文件夹中的 tpl 是模版文件, htmlwebpackPlugin 会按照模版文件生成对应格式的 tpl 文件,
// 3. dist 中的 tpl 文件会被 controller/view.js 读取, 通过 koa-nunjucks-2 解析, 并将数据渲染到模版文件中, 并返回给用户 
// 3. 通过 viewController.renderPage 方法会读取 dist 到一个页面渲染的函数,配置在 router.get 中, 使得用户可以访问到对应的页面
// 总结开发环境运行渲染一个页面的流程:  
//    vue文件 --> webpack --> dist文件夹 --> webpackDEVServer + httpService --> router.js(koa-router) -->  controller(koa-nunjucks-2解析渲染) 


module.exports = (app, router) => {
  const { view: viewController } = app.controller;

  // 用户输入 http://localhost:3000/view/xxx 时，渲染对应页面
  router.get('/view/:page', viewController.renderPage.bind(viewController))
  router.get('/view/:page/*', viewController.renderPage.bind(viewController))
}