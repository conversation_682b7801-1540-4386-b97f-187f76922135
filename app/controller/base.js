module.exports = (app) => class BaseController {
  /**
   * controlller 基类
   * 统一收拢 controller 相关的公共方法
   */
  constructor() {
    this.app = app;
    this.config = app.config;
    this.service = app.service;
  }

  /**
   * API 处理成功返回结构
   * @param {Object} ctx ctx 上下文
   * @param {Object} data 返回数据
   * @param {Object} metadata 附加数据
   */
  success(ctx, data = {}, metadata = {}) {
    ctx.status = 200;
    ctx.body = {
      success: true,
      data,
      metadata
    }
  }

  /**
  * API 处理失败返回结构
  * @param {Object} ctx ctx 上下文
  * @param {Object} message 返回数据
  * @param {Object} code 错误码
  */
  fail(ctx, message, code) {
    ctx.body = {
      success: false,
      message,
      code
    }
  }
}