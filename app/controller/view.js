module.exports = (app) => {
  return class ViewController {
    /**
     *  渲染页面
     * @param {object} ctx  上下文对象
     */
    async renderPage(ctx) {
      // ctx.render(template, data)是 koa-nunjucks-2 提供的方法 
      // 它会给router.get 提供一个 render 方法,通过render方法调用模板渲染功能;
      // koa-nunjucks-2 会解析模版文件和ctx中的数据, 如果模板文件中的 {{ name }} 与 ctx data中的name 同名, 就会被赋值渲染到模版中;
      // 所以模版文件中的 {{name}}, 打开页面时直接显示的就是 ctx 中 data.name 的值
      await ctx.render(`dist/entry.${ctx.params.page}`, {
        projKey: ctx.query?.proj_key,
        name: app.options.name,
        env: app.env.get(),
        options: JSON.stringify(app.options)
      })
    }
  }
}