const glob = require('glob');
const path = require('path')
const { sep } = path

/**
 * router-schema loader
 * @param {object} app koa实例 
 * 
 * 通过 'json-schema' & 'ajv' 对 api规则进行约束,配合 api-params-verify 中间件使用
 * 
 * app/router-schema/api1.js  // { 'api1/data/getDetail' :{} } 
 * app/router-schema/api2.js  // { 'api2/data/getDetail' :{} } 
 * ...
 * 
 * 输出:
 *   app.routerSchema = {
 *      'api1/data/getDetail':{},
 *      'api2/data/getDetail':{},
 *     ...
 *   }
 */

module.exports = (app) => {
  let routerSchema = {}

  // 读取 elpis/app/router-schema/**/**.js 所有文件 
  const elpisRouterSchemaPath = path.resolve(__dirname, `..${sep}..${sep}app${sep}router-schema`);
  const elpisFileList = glob.sync(path.resolve(elpisRouterSchemaPath, `.${sep}**${sep}**.js`));
  elpisFileList.forEach(file => {
    handleFile(file)
  })

  // 读取业务 app/router-schema/**/**.js 所有文件 
  const businessRouterSchemaPath = path.resolve(app.businessPath, `.${sep}router-schema`);
  const businessFileList = glob.sync(path.resolve(businessRouterSchemaPath, `.${sep}**${sep}**.js`));

  businessFileList.forEach(file => {
    handleFile(file)
  })

  // 注册所有 routerSchema, 使得 app.routerSchema 可以访问

  function handleFile(file) {
    routerSchema = {
      ...routerSchema,
      ...require(path.resolve(file))
    }
  }
  app.routerSchema = routerSchema
}