const KoaRouter = require('koa-router');
const glob = require('glob');
const path = require('path');
const { sep } = path;

/**
 * router loader
 * @param {object} app koa实例
 * 
 * 解析所有 app/router/ 下所有 js 文件, 加载到 KoaRouter 下
 * 
 */

module.exports = (app) => {
  // 实例化所有路由
  const router = new KoaRouter();

  // 找到elpis路由文件路径
  const elpisRouterPath = path.resolve(__dirname, `..${sep}..${sep}app${sep}router`);
  // 注册所有elpis路由
  const elpisFileList = glob.sync(path.resolve(elpisRouterPath, `.${sep}**${sep}**.js`));
  elpisFileList.forEach(file => {
    require(path.resolve(file))(app, router);
  });


  // 找到业务路由文件路径
  const buinessRouterPath = path.resolve(app.businessPath, `.${sep}router`);
  // 注册所有业务路由
  const buinessFileList = glob.sync(path.resolve(buinessRouterPath, `.${sep}**${sep}**.js`));
  buinessFileList.forEach(file => {
    require(path.resolve(file))(app, router);
  });

  // 路由兜底(健壮性)
  router.get('*', async (ctx, next) => {
    const homePage = app?.options?.homePage ?? '/';
    if (ctx.path === homePage) {
      // 避免重定向循环
      ctx.status = 404;
      ctx.body = 'Not Found';
    } else {
      ctx.status = 302; // 临时重定向
      ctx.redirect(homePage);
    }
  })

  // 路由注册到 app 上
  app.use(router.routes());
  app.use(router.allowedMethods());
}