# elpis  
## 一个企业级应用框架，通过全栈实现

### model 配置

```javascript
{
  mode: "dashboard", // 模板类型，不同模板类型对应不一样的模板数据结构
  name: "", // 名称
  desc: "", // 描述
  icon: "", // icon
  homePage: "", // 首页(项目配置)

  // 头部菜单
  menu: [
    {
      key: "", // 菜单唯一描述
      name: "", // 菜单名称
      menuType: "", // 枚举值:group/module

      // 当 menuType == group 时，可填
      subMenu: [
        {
          // 可递归 menuItem
        },
      ],

      // 当 menuType == module 时，可填
      moduleType: "", // 枚举值:sider/iframe/custom/schema

      // 当 moduleType == sider 时
      siderConfig: {
        menu: [
          {
            // 可递归 menuItem(除 moduleType == side)
          },
        ],
      },

      // 当 moduleType == iframe 时
      iframeConfig: {
        path: "", // iframe 路径
      },

      // 当 moduleType == custom 时
      customConfig: {
        path: "", // 自定义路由路径
      },

      // 当 moduleType == schema 时
      schemaConfig: {
        api: "/api/user", // 数据源 API(遵循 RESTFUL 规范)
        schema: {
          // 板块数据结构
          type: "object",
          properties: {
            key: {
              ...schema, // 标准 schema 配置
              type: "", // 字段类型
              label: "", // 字段的中文名
              // 字段在 table 中的相关配置
              tableOption: {
                ...elTableColumnConfig, // 标准 el-table-column 配置
                toFixed: 0, // 保留几位小数
                visible: true, // 默认为 true（false 时，表示不在表单中展示）
              },
              // 字段在 search-bar 中的相关配置
              searchOption: {
                ...elComponentConfig, // 标准 el-component-column 配置
                comType: "", // 配置组件类型 input/select/...
                default: "", // 默认值

                // comType === 'select'
                enumList: [], // 下拉框可选项

                // comType === 'dynamicSelect'
                api: "",
              },
              // 字段在不同动态 component 中的相关配置，前缀对应 componentConfig 中的键值
              // 如：componentConfig.createForm 这里对应 createFormOption
              // 字段在 createForm 中的相关配置
              createFormOption: {
                ...elComponentConfig, // 标准 el-component 配置
                comType: "", // 配置组件类型 input/select/input-number/...
                visible: true, // 是否展示（true/false），默认为 true
                disabled: false, // 是否禁用（true/false），默认为 false
                default: "", // 默认值

                // comType === 'select'
                enumList: [], // 枚举列表
              },
              // 字段在 editForm 中的相关配置
              editFormOption: {
                ...elComponentConfig, // 标准 el-component 配置
                comType: "", // 配置组件类型 input/select/input-number/...
                visible: true, // 是否展示（true/false），默认为 true
                disabled: false, // 是否禁用（true/false），默认为 false
                default: "", // 默认值

                // comType ==='select'
                enumList: [], // 枚举列表
              },
              detailPanelOption: {
                ...elComponentConfig, // 标准 el-component 配置
              },
            },
          },
          required: [], // 必填字段
        },
      },
      // table 相关配置
      tableConfig: {
        headerButtons: [
          {
            label: "", // 按钮中文名
            eventKey: "", // 按钮事件名
            eventOption: {
              // 当 eventKey == "showComponent"
              comName: "", // 组件名称
            }, // 按钮事件具体配置
            ...elButtonConfig, // 标准 el-button 配置
          },
        ],
        rowButtons: [
          {
            label: "", // 按钮中文名
            eventKey: "", // 按钮事件名
            eventOption: {
              // 当 eventKey == "showComponent"
              comName: "", // 组件名称
              // 当 eventKey == "remove"
              params: {
                // paramKey = 参数的键值
                // paramValue = 参数值，格式为 schema::tableKey，到 table 中找相应的字段
                paramKey: rowValueKey,
              },
            }, // 按钮事件具体配置
            ...elButtonConfig, // 标准 el-button 配置
          },
        ],
      },
      // search-bar 相关配置
      searchConfig: {},
      // 动态组件 相关配置
      componentConfig: {
        // create-form 表单相关配置
        createForm: {
          title: "", // 表单标题
          saveBtnText: "", // 保存按钮文案
        },
        // edit-form 表单相关配置
        editForm: {
          mainKey: "", // 表单主键，用于唯一标识要修改的数据对象
          title: "", // 表单标题
          saveBtnText: "", // 保存按钮文案
        },
        // detail-panel 相关配置
        detailPanel: {
          mainKey: "", // 表单主键，用于唯一标识要修改的数据对象
          title: "", // 表单标题
        },
        // ...支持用户动态拓展
      },
    },
  ],
};
```

### 服务端启动

```javascript
const { serverStart } = require("@fangkong/elpis");
// 启动 elpis 服务
const app = serverStart();
```

### 自定义服务器

- router-schema
- router
- controller
- service
- extend
- config

### 前端构建

```javascript
const { frontendBuild } = require("@fangkong/elpis");
// 编译构建前端工程
frontendBuild(process.env._ENV);
```

### 自定义页面扩展

* 在 `app/pages/` 目录下写入口 entry.xxx.js

### dashboard / custom-view 自定义页面扩展

* 在 `app/pages/dashboard/xxxx` 下写页面

### dashboard / schema-view / components 动态组件扩展

1. 在 `app/pages/dashboard/complex-view/schema-view/components` 下写组件
2. 配置到 `app/pages/dashboard/complex-view/schema-view/components/components-config.js` 中

### schema-form 控件扩展

1. 在 `app/widgets/schema-form/complex-view` 下写控件
2. 配置到 `app/widgets/schema-form/form-item-config.js` 中

### schema-search-bar 控件扩展

1. 在 `app/widgets/schema-search-bar/complex-view` 下写控件
2. 配置到 `app/widgets/schema-search-bar/search-item-config.js` 中
