const ElpisCore = require('./elpis-core');
// 引入前端工程化构建方法
const FEBuildDay = require('./app/webpack/dev');
const FEBuildProd = require('./app/webpack/prod');

module.exports = {
  /**
   * 服务端基础
   */
  Controller: {
    Base: require('./app/controller/base.js')
  },
  Service: {
    Base: require('./app/service/base.js')
  },

  /**
   *  编译构建前端工程
   *  @params env 环境变量 dev/prod
  */
  frontendBuild(env) {
    if (env === 'local') {
      FEBuildDay();
    } else if (env === 'production') {
      FEBuildProd();
    }
  },

  /**
   * 启动 elpis
   * @param {*} options 项目配置， 透传到 elpis-core
   */
  serverStart(options = {}) {
    const app = ElpisCore.start(options);
    return app;
  }
}